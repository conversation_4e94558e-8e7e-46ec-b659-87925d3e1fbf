<?php

namespace App\Console\Commands;

use App\Models\OpenAiProject;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class MonitorOpenAiStorage extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'openai:monitor-storage
                            {--threshold=90 : Storage percentage threshold for warnings}
                            {--alert : Send alerts for projects nearing capacity}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Monitor OpenAI project storage usage and alert on capacity issues';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $threshold = (float) $this->option('threshold');
        $sendAlerts = $this->option('alert');

        $this->info("Monitoring OpenAI project storage usage (threshold: {$threshold}%)");
        $this->newLine();

        $projects = OpenAiProject::where('is_active', true)->get();

        if ($projects->isEmpty()) {
            $this->warn('No active OpenAI projects found.');
            return;
        }

        $headers = ['Project', 'Storage Used (GB)', 'Percentage', 'Status', 'Cases', 'Documents'];
        $rows = [];
        $warningProjects = [];

        foreach ($projects as $project) {
            $storageInfo = $project->getStorageInfo();
            $caseCount = $project->caseFiles()->count();
            $documentCount = $project->caseFiles()->withCount('documents')->get()->sum('documents_count');

            $status = $this->getStatusText($storageInfo['percentage'], $threshold);

            if ($storageInfo['percentage'] > $threshold) {
                $warningProjects[] = $project;
            }

            $rows[] = [
                $project->name,
                $storageInfo['used_gb'],
                number_format($storageInfo['percentage'], 1) . '%',
                $status,
                $caseCount,
                $documentCount
            ];
        }

        $this->table($headers, $rows);

        // Show summary
        $this->newLine();
        $this->info('Summary:');
        $this->line("Total active projects: {$projects->count()}");
        $this->line("Projects over {$threshold}% capacity: " . count($warningProjects));

        if (!empty($warningProjects)) {
            $this->newLine();
            $this->warn('⚠️  Projects nearing capacity:');

            foreach ($warningProjects as $project) {
                $storageInfo = $project->getStorageInfo();
                $this->line("  • {$project->name}: {$storageInfo['used_gb']}GB ({$storageInfo['percentage']}%)");

                if ($sendAlerts) {
                    $this->logStorageAlert($project, $storageInfo);
                }
            }

            if ($sendAlerts) {
                $this->info('Storage alerts have been logged.');
            }
        } else {
            $this->info('✅ All projects are within acceptable storage limits.');
        }

        return 0;
    }

    /**
     * Get status text based on storage percentage.
     */
    private function getStatusText(float $percentage, float $threshold): string
    {
        if ($percentage >= 95) {
            return '🔴 Critical';
        } elseif ($percentage >= $threshold) {
            return '🟡 Warning';
        } else {
            return '🟢 OK';
        }
    }

    /**
     * Log storage alert for monitoring systems.
     */
    private function logStorageAlert(OpenAiProject $project, array $storageInfo): void
    {
        Log::warning('OpenAI project nearing storage capacity', [
            'project_id' => $project->id,
            'project_name' => $project->name,
            'storage_used_gb' => $storageInfo['used_gb'],
            'storage_percentage' => $storageInfo['percentage'],
            'remaining_gb' => $storageInfo['remaining_gb'],
            'case_count' => $project->caseFiles()->count(),
            'alert_type' => 'storage_capacity_warning'
        ]);
    }
}
