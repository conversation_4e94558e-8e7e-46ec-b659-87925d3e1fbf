<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Fortify\TwoFactorAuthenticatable;
use Laravel\Jetstream\HasProfilePhoto;
use Laravel\Jetstream\HasTeams;
use Laravel\Sanctum\HasApiTokens;
use Laravel\Cashier\Billable;

class User extends Authenticatable
{
    use HasApiTokens;
    use Billable;

    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory;
    use HasProfilePhoto;
    use HasTeams;
    use Notifiable;
    use TwoFactorAuthenticatable;

    public function caseFiles(): HasMany
    {
        return $this->hasMany(CaseFile::class);
    }

    public function collaborations(): HasMany
    {
        return $this->hasMany(CaseCollaborator::class);
    }

    /**
     * Get the private threads that the user is participating in.
     */
    public function privateThreads(): BelongsToMany
    {
        return $this->belongsToMany(PrivateThread::class, 'private_thread_participants')
            ->withPivot('last_read_at')
            ->withTimestamps();
    }

    /**
     * Get the invoices created by the user.
     */
    public function sentInvoices(): HasMany
    {
        return $this->hasMany(Invoice::class, 'creator_id');
    }

    /**
     * Get the invoices received by the user.
     */
    public function receivedInvoices(): HasMany
    {
        return $this->hasMany(Invoice::class, 'recipient_id');
    }

    /**
     * Get the invoice payments made by the user.
     */
    public function invoicePayments(): HasMany
    {
        return $this->hasMany(InvoicePayment::class, 'paid_by_id');
    }

    /**
     * Get the user's Stripe Connect account.
     */
    public function connectAccount()
    {
        return $this->hasOne(ConnectAccount::class);
    }

    /**
     * Get the private messages sent by the user.
     */
    public function privateMessages(): HasMany
    {
        return $this->hasMany(PrivateMessage::class);
    }

    /**
     * Get the user's credit balance.
     */
    public function credits()
    {
        return $this->hasOne(UserCredit::class);
    }

    /**
     * Get the user's credit transactions.
     */
    public function creditTransactions(): HasMany
    {
        return $this->hasMany(CreditTransaction::class);
    }

    /**
     * Get the user's current credit balance.
     *
     * @return int
     */
    public function getCreditBalanceAttribute(): int
    {
        return $this->credits ? $this->credits->balance : 0;
    }

    /**
     * Get the model name for the subscription.
     *
     * @return string
     */
    public function subscriptions()
    {
        return $this->hasMany(Subscription::class, 'user_id');
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'username',
        'email',
        'password',
        'language',
        'is_attorney',
        'bar_card_number',
        'zip_code',
        'latitude',
        'longitude'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'two_factor_recovery_codes',
        'two_factor_secret',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<int, string>
     */
    protected $appends = [
        'profile_photo_url',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }
}
